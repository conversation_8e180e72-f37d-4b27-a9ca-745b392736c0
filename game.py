"""
连连看游戏核心逻辑
"""
import pygame
import random
from constants import GRID_SIZE, GRID_WIDTH, GRID_HEIGHT, TILE_SIZE, MARGIN, COLORS, BG_COLOR

class Game:
    """
    游戏主类，处理游戏逻辑和绘制
    """
    def __init__(self, screen):
        """
        初始化游戏
        :param screen: Pygame屏幕对象
        """
        self.screen = screen
        self.grid = []
        self.selected_tile = None
        self.reset()
    
    def reset(self):
        """
        重置游戏状态
        """
        # 创建初始网格（所有位置为空）
        self.grid = [[0 for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]
        self.selected_tile = None
        self._generate_tiles()  # 生成初始方块
    
    def _generate_tiles(self):
        """
        生成游戏方块
        """
        # 创建方块对
        tiles = []
        for color in COLORS:
            tiles.extend([color] * 2)  # 每种颜色两个方块
        
        random.shuffle(tiles)  # 打乱顺序
        
        # 放置方块到网格
        for i in range(GRID_HEIGHT):
            for j in range(GRID_WIDTH):
                if tiles:
                    self.grid[i][j] = tiles.pop()
    
    def handle_click(self, pos):
        """
        处理鼠标点击事件
        :param pos: 点击位置(x, y)
        """
        # 计算点击的网格坐标
        x, y = pos
        col = (x - MARGIN) // TILE_SIZE
        row = (y - MARGIN) // TILE_SIZE
        
        # 检查是否在有效范围内
        if 0 <= row < GRID_HEIGHT and 0 <= col < GRID_WIDTH:
            # 如果点击的是有方块的位置
            if self.grid[row][col] != 0:
                # 如果没有选中的方块，则选中当前方块
                if self.selected_tile is None:
                    self.selected_tile = (row, col)
                # 如果已经选中了一个方块
                else:
                    prev_row, prev_col = self.selected_tile
                    # 如果点击的是同一个方块，取消选择
                    if (row, col) == (prev_row, prev_col):
                        self.selected_tile = None
                    # 如果是不同的方块
                    else:
                        # 检查是否可以连接
                        if self._can_connect(prev_row, prev_col, row, col):
                            # 消除两个方块
                            self.grid[prev_row][prev_col] = 0
                            self.grid[row][col] = 0
                        # 无论是否连接成功，都取消当前选择
                        self.selected_tile = None
    
    def _can_connect(self, r1, c1, r2, c2):
        """
        检查两个方块是否可以连接
        :return: 是否可以连接
        """
        # 如果两个方块类型不同
        if self.grid[r1][c1] != self.grid[r2][c2]:
            return False
            
        # 简单实现：只允许直接相邻（实际连连看需要实现路径检查）
        # 这里简化了逻辑，实际项目中需要实现完整的路径搜索算法
        return (
            (abs(r1 - r2) == 1 and c1 == c2) or  # 垂直相邻
            (abs(c1 - c2) == 1 and r1 == r2)     # 水平相邻
        )
    
    def draw(self):
        """
        绘制游戏界面
        """
        # 绘制背景
        self.screen.fill(BG_COLOR)
        
        # 绘制网格
        for row in range(GRID_HEIGHT):
            for col in range(GRID_WIDTH):
                if self.grid[row][col] != 0:
                    # 计算方块位置
                    x = MARGIN + col * TILE_SIZE
                    y = MARGIN + row * TILE_SIZE
                    
                    # 绘制方块
                    pygame.draw.rect(
                        self.screen,
                        self.grid[row][col],
                        (x, y, TILE_SIZE - GRID_SIZE, TILE_SIZE - GRID_SIZE)
                    )
                    
                    # 如果当前方块被选中，绘制选中框
                    if self.selected_tile == (row, col):
                        pygame.draw.rect(
                            self.screen,
                            (255, 255, 0),  # 黄色边框
                            (x-2, y-2, TILE_SIZE - GRID_SIZE + 4, TILE_SIZE - GRID_SIZE + 4),
                            2  # 边框宽度
                        )
        
        # 绘制游戏状态信息
        font = pygame.font.SysFont(None, 36)
        text = font.render("按R键重置游戏", True, (255, 255, 255))
        self.screen.blit(text, (20, SCREEN_HEIGHT - 40))