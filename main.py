"""
连连看游戏主入口
"""
import pygame
from game import Game
from constants import SCREEN_WIDTH, SCREEN_HEIGHT, FPS

def main():
    """
    游戏主函数
    """
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("连连看小游戏")
    clock = pygame.time.Clock()
    
    game = Game(screen)
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # 左键点击
                    game.handle_click(event.pos)
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:  # 按R键重置游戏
                    game.reset()
        
        screen.fill((0, 0, 0))  # 清屏
        game.draw()  # 绘制游戏
        pygame.display.flip()  # 更新显示
        clock.tick(FPS)  # 控制帧率
    
    pygame.quit()

if __name__ == "__main__":
    main()