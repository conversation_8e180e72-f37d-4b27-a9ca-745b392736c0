# 推理过程显示控制

## 功能概述

DeepSeek Engineer 现在支持控制是否显示 AI 的推理过程（Reasoning）。默认情况下，推理过程是隐藏的，以提供更清洁的用户体验。

## 使用方法

### 1. 默认模式（隐藏推理）
```bash
python deepseek-eng.py
```

在这种模式下：
- ✅ AI 的最终回答会正常显示
- ❌ 推理过程（💭 Reasoning）会被隐藏
- 🎯 界面更简洁，专注于结果

### 2. 显示推理模式
```bash
python deepseek-eng.py --show-reasoning
```

在这种模式下：
- ✅ AI 的最终回答会正常显示
- ✅ 推理过程（💭 Reasoning）会完整显示
- 🔍 可以看到 AI 的思考过程

## 界面对比

### 隐藏推理模式
```
🐋 Seeking...

🤖 Assistant> 我来帮您分析这个问题...
```

### 显示推理模式
```
🐋 Seeking...

💭 Reasoning:
用户询问了关于代码的问题，我需要先分析代码结构，然后提供解决方案...

🤖 Assistant> 我来帮您分析这个问题...
```

## 配置说明

### 代码中的配置变量
```python
# 配置选项
SHOW_REASONING = False  # 设置为 True 可显示推理过程，False 则隐藏
```

### 运行时状态显示
程序启动时会显示当前的推理显示状态：

```
🐋 DeepSeek Engineer with Function Calling
Powered by DeepSeek-R1 with Chain-of-Thought Reasoning
当前设置: 隐藏推理过程
```

或

```
🐋 DeepSeek Engineer with Function Calling
Powered by DeepSeek-R1 with Chain-of-Thought Reasoning
当前设置: 显示推理过程
```

## 技术实现

### 主要修改点

1. **配置变量**：添加了 `SHOW_REASONING` 全局变量
2. **条件显示**：推理内容仍然被收集，但只在配置允许时显示
3. **命令行参数**：支持 `--show-reasoning` 参数
4. **界面调整**：根据是否显示推理调整换行和间距

### 代码逻辑
```python
# 收集推理内容（始终执行）
reasoning_content += chunk.choices[0].delta.reasoning_content

# 只有在配置允许时才显示
if SHOW_REASONING:
    console.print(chunk.choices[0].delta.reasoning_content, end="")
```

## 使用建议

### 何时隐藏推理
- 🎯 **日常使用**：专注于结果，界面更简洁
- 🚀 **生产环境**：减少输出噪音
- 📱 **移动设备**：节省屏幕空间

### 何时显示推理
- 🔍 **调试模式**：了解 AI 的思考过程
- 📚 **学习目的**：观察 AI 如何分析问题
- 🛠️ **开发阶段**：验证 AI 的推理逻辑

## 注意事项

1. **性能影响**：隐藏推理不会影响 AI 的思考质量，只是不显示过程
2. **内容完整性**：推理内容仍然被收集，可用于调试或日志记录
3. **动态切换**：目前需要重启程序来切换模式，未来可考虑添加运行时切换功能

## 未来改进

- [ ] 运行时动态切换推理显示
- [ ] 配置文件支持
- [ ] 推理内容的日志记录选项
- [ ] 推理内容的摘要显示模式
