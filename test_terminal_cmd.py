#!/usr/bin/env python3
"""
测试 run_terminal_cmd 功能的简单脚本
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from deepseek_eng import run_terminal_command

def test_terminal_command():
    """测试终端命令功能"""
    print("🧪 测试 run_terminal_command 功能")
    
    # 测试简单的 echo 命令
    result = run_terminal_command(
        command="echo 'Hello from terminal!'",
        description="测试简单的 echo 命令",
        working_directory=None
    )
    
    print("📋 执行结果:")
    print(result)

if __name__ == "__main__":
    test_terminal_command()
