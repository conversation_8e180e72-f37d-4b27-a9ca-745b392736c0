body {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a2a6c, #b21f1f, #fdbb2d);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.container {
    text-align: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

h1 {
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 0 0 10px #00ffff;
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    font-size: 1.2em;
}

#gameCanvas {
    background-color: #111;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.controls {
    margin-top: 20px;
}

button {
    background: #00b4db;
    border: none;
    color: white;
    padding: 10px 20px;
    margin: 0 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s;
}

button:hover {
    background: #0083b0;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}