<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL连连看游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>WebGL连连看</h1>
        <div class="game-info">
            <p>分数: <span id="score">0</span></p>
            <p>剩余时间: <span id="timer">120</span>秒</p>
        </div>
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        <div class="controls">
            <button id="restart">重新开始</button>
            <button id="hint">提示</button>
        </div>
    </div>
    <script src="shaders.js"></script>
    <script src="game.js"></script>
</body>
</html>