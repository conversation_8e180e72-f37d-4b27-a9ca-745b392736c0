// 游戏配置
const ROWS = 6;
const COLS = 6;
const TILE_TYPES = ['🍎', '🍐', '🍊', '🍋', '🍇', '🍓', '🍒', '🍑', '🍍', '🥭', '🥥', '🥝'];

// 游戏状态
let board = [];
let selectedTile = null;
let score = 0;
let timeLeft = 180; // 3分钟
let timer;
let hintTimeout; // 提示计时器

// DOM元素
const gameBoard = document.getElementById('game-board');
const messageEl = document.getElementById('message');
const restartBtn = document.getElementById('restart-btn');
const scoreEl = document.createElement('div');
scoreEl.id = 'score';
scoreEl.className = 'info';
const timerEl = document.createElement('div');
timerEl.id = 'timer';
timerEl.className = 'info';
const hintBtn = document.createElement('button');
hintBtn.id = 'hint-btn';
hintBtn.className = 'btn';
hintBtn.textContent = '提示';

// 添加信息面板
const infoPanel = document.createElement('div');
infoPanel.className = 'info-panel';
infoPanel.appendChild(scoreEl);
infoPanel.appendChild(timerEl);
infoPanel.appendChild(hintBtn);
document.querySelector('.game-container').insertBefore(infoPanel, gameBoard);

// 自动提示功能
function autoHint() {
    // 找到一对可连接的格子
    for (let i = 0; i < ROWS; i++) {
        for (let j = 0; j < COLS; j++) {
            if (isTileEmpty(i, j)) continue;
            
            for (let k = 0; k < ROWS; k++) {
                for (let l = 0; l < COLS; l++) {
                    if (i === k && j === l) continue;
                    if (isTileEmpty(k, l)) continue;
                    
                    if (board[i][j] === board[k][l] && 
                        canConnect({row: i, col: j}, {row: k, col: l})) {
                        
                        // 高亮显示这对格子
                        highlightTiles(i, j, k, l);
                        return true;
                    }
                }
            }
        }
    }
    
    return false; // 没有找到可连接的格子
}

// 高亮显示提示格子
function highlightTiles(row1, col1, row2, col2) {
    const tile1 = document.querySelector(`.tile[data-row="${row1}"][data-col="${col1}"]`);
    const tile2 = document.querySelector(`.tile[data-row="${row2}"][data-col="${col2}"]`);
    
    tile1.classList.add('hint');
    tile2.classList.add('hint');
    
    // 3秒后取消高亮
    hintTimeout = setTimeout(() => {
        tile1.classList.remove('hint');
        tile2.classList.remove('hint');
    }, 3000);
}

// 更新分数显示
function updateScore() {
    scoreEl.textContent = `分数: ${score}`;
}

// 更新计时器
function updateTimer() {
    timeLeft--;
    
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    timerEl.textContent = `时间: ${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    
    if (timeLeft <= 0) {
        clearInterval(timer);
        messageEl.textContent = '时间到！游戏结束';
        hintBtn.disabled = true;
        
        // 禁用所有格子
        document.querySelectorAll('.tile').forEach(tile => {
            tile.style.pointerEvents = 'none';
        });
    }
}

// 播放音效
function playSound(type) {
    // 在实际应用中，这里应该播放音频文件
    // 这里使用控制台日志模拟
    console.log(`播放音效: ${type}`);
}

// 初始化游戏
function initGame() {
    // 重置游戏状态
    board = [];
    selectedTile = null;
    score = 0;
    timeLeft = 180;
    messageEl.textContent = '';
    
    // 清除之前的计时器
    clearInterval(timer);
    clearTimeout(hintTimeout);
    
    // 启动新计时器
    timer = setInterval(updateTimer, 1000);
    
    // 更新分数显示
    updateScore();
    
    // 重置提示按钮
    hintBtn.disabled = false;
    hintBtn.textContent = '提示';
    
    // 生成图标对
    let pairs = [];
    const totalPairs = (ROWS * COLS) / 2;
    
    // 随机选择图标类型
    const selectedTypes = [];
    while (selectedTypes.length < totalPairs) {
        const randomType = TILE_TYPES[Math.floor(Math.random() * TILE_TYPES.length)];
        if (!selectedTypes.includes(randomType)) {
            selectedTypes.push(randomType);
        }
    }
    
    // 创建成对图标
    selectedTypes.forEach(type => {
        pairs.push(type, type);
    });
    
    // 随机打乱顺序
    shuffleArray(pairs);
    
    // 创建游戏板
    gameBoard.innerHTML = '';
    for (let i = 0; i < ROWS; i++) {
        board[i] = [];
        for (let j = 0; j < COLS; j++) {
            const index = i * COLS + j;
            const tileValue = pairs[index];
            board[i][j] = tileValue;
            
            const tile = document.createElement('div');
            tile.className = 'tile';
            tile.dataset.row = i;
            tile.dataset.col = j;
            tile.textContent = tileValue;
            tile.addEventListener('click', () => onTileClick(i, j));
            gameBoard.appendChild(tile);
        }
    }
}

// 打乱数组（Fisher-Yates算法）
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

// 处理格子点击
function onTileClick(row, col) {
    const tile = document.querySelector(`.tile[data-row="${row}"][data-col="${col}"]`);
    
    // 如果格子已被消除，忽略点击
    if (tile.classList.contains('removed')) return;
    
    // 如果已选中当前格子，取消选中
    if (selectedTile && selectedTile.row === row && selectedTile.col === col) {
        tile.classList.remove('selected');
        selectedTile = null;
        return;
    }
    
    // 选中格子
    tile.classList.add('selected');
    
    // 如果没有已选中的格子，记录当前选中
    if (!selectedTile) {
        selectedTile = { row, col, value: board[row][col] };
        return;
    }
    
    // 如果有两个选中的格子，检查是否匹配
    const firstTile = selectedTile;
    const secondTile = { row, col, value: board[row][col] };
    
    // 检查是否相同且可连接
    if (firstTile.value === secondTile.value && 
        canConnect(firstTile, secondTile)) {
        // 消除格子
        removeTiles(firstTile, secondTile);
        
        // 检查游戏是否结束
        if (checkGameWin()) {
            messageEl.textContent = '恭喜！游戏胜利！';
        }
    } else {
        // 不匹配，取消第一个格子的选中状态
        document.querySelector(`.tile[data-row="${firstTile.row}"][data-col="${firstTile.col}"]`)
            .classList.remove('selected');
    }
    
    // 重置选中状态
    selectedTile = null;
}

// 检查两个格子是否可以连接
function canConnect(tileA, tileB) {
    // 直接连接（0个拐点）
    if (checkDirectLine(tileA, tileB)) return true;
    
    // 一个拐点连接
    if (checkOneCorner(tileA, tileB)) return true;
    
    // 两个拐点连接
    if (checkTwoCorners(tileA, tileB)) return true;
    
    return false;
}

// 检查直线连接（0拐点）
function checkDirectLine(tileA, tileB) {
    // 同行
    if (tileA.row === tileB.row) {
        const row = tileA.row;
        const start = Math.min(tileA.col, tileB.col);
        const end = Math.max(tileA.col, tileB.col);
        
        for (let col = start + 1; col < end; col++) {
            if (!isTileEmpty(row, col)) return false;
        }
        return true;
    }
    
    // 同列
    if (tileA.col === tileB.col) {
        const col = tileA.col;
        const start = Math.min(tileA.row, tileB.row);
        const end = Math.max(tileA.row, tileB.row);
        
        for (let row = start + 1; row < end; row++) {
            if (!isTileEmpty(row, col)) return false;
        }
        return true;
    }
    
    return false;
}

// 检查一个拐点连接
function checkOneCorner(tileA, tileB) {
    // 检查拐点1（水平-垂直）
    const corner1 = { row: tileA.row, col: tileB.col };
    if (isTileEmpty(corner1.row, corner1.col)) {
        if (checkDirectLine(tileA, corner1) && 
            checkDirectLine(corner1, tileB)) {
            return true;
        }
    }
    
    // 检查拐点2（垂直-水平）
    const corner2 = { row: tileB.row, col: tileA.col };
    if (isTileEmpty(corner2.row, corner2.col)) {
        if (checkDirectLine(tileA, corner2) && 
            checkDirectLine(corner2, tileB)) {
            return true;
        }
    }
    
    return false;
}

// 检查两个拐点连接（优化算法）
function checkTwoCorners(tileA, tileB) {
    // 检查水平方向的可能路径
    for (let col = 0; col < COLS; col++) {
        if (col === tileA.col || col === tileB.col) continue;
        
        // 检查路径: tileA -> (tileA.row, col) -> (tileB.row, col) -> tileB
        if (isTileEmpty(tileA.row, col) && 
            isTileEmpty(tileB.row, col) &&
            checkDirectLine({row: tileA.row, col: tileA.col}, {row: tileA.row, col: col}) &&
            checkDirectLine({row: tileA.row, col: col}, {row: tileB.row, col: col}) &&
            checkDirectLine({row: tileB.row, col: col}, {row: tileB.row, col: tileB.col})) {
            return true;
        }
    }
    
    // 检查垂直方向的可能路径
    for (let row = 0; row < ROWS; row++) {
        if (row === tileA.row || row === tileB.row) continue;
        
        // 检查路径: tileA -> (row, tileA.col) -> (row, tileB.col) -> tileB
        if (isTileEmpty(row, tileA.col) && 
            isTileEmpty(row, tileB.col) &&
            checkDirectLine({row: tileA.row, col: tileA.col}, {row: row, col: tileA.col}) &&
            checkDirectLine({row: row, col: tileA.col}, {row: row, col: tileB.col}) &&
            checkDirectLine({row: row, col: tileB.col}, {row: tileB.row, col: tileB.col})) {
            return true;
        }
    }
    
    return false;
}

// 检查格子是否为空（已被消除）
function isTileEmpty(row, col) {
    const tile = document.querySelector(`.tile[data-row="${row}"][data-col="${col}"]`);
    return tile.classList.contains('removed');
}

// 移除匹配的格子
function removeTiles(tileA, tileB) {
    const tileElA = document.querySelector(`.tile[data-row="${tileA.row}"][data-col="${tileA.col}"]`);
    const tileElB = document.querySelector(`.tile[data-row="${tileB.row}"][data-col="${tileB.col}"]`);
    
    // 添加移除动画
    tileElA.classList.add('removed');
    tileElB.classList.add('removed');
    
    // 更新游戏板状态
    board[tileA.row][tileA.col] = null;
    board[tileB.row][tileB.col] = null;
    
    // 增加分数
    score += 10;
    updateScore();
    
    // 播放消除音效
    playSound('match');
}

// 检查游戏是否胜利
function checkGameWin() {
    for (let row = 0; row < ROWS; row++) {
        for (let col = 0; col < COLS; col++) {
            if (!isTileEmpty(row, col)) {
                return false;
            }
        }
    }
    return true;
}

// 提示按钮事件
hintBtn.addEventListener('click', () => {
    // 使用提示会扣分
    if (score >= 5) {
        score -= 5;
        updateScore();
        
        if (!autoHint()) {
            messageEl.textContent = '没有可用的提示';
        }
    } else {
        messageEl.textContent = '分数不足，无法使用提示';
    }
});

// 重新开始游戏
restartBtn.addEventListener('click', initGame);

// 初始化游戏
initGame();