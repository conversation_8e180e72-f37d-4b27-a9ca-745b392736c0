# list_dir 功能演示

## 功能概述

`list_dir` 是一个新添加的工具函数，用于列出指定目录的内容，包括文件和子目录的详细信息。

## 功能特性

### 1. 基本目录列表
- 列出指定目录中的所有文件和子目录
- 显示文件大小（格式化为 B, KB, MB, GB, TB）
- 显示文件修改时间
- 提供目录和文件的统计信息

### 2. 高级选项
- **show_hidden**: 是否显示隐藏文件和目录（默认为 false）
- **recursive**: 是否递归列出子目录内容（默认为 false）
- **max_depth**: 递归时的最大深度（默认为 2）

### 3. 安全特性
- 路径标准化和安全检查
- 权限验证
- 错误处理和异常捕获

## 使用示例

### 示例 1: 基本用法
```json
{
  "function": "list_dir",
  "arguments": {
    "directory_path": "."
  }
}
```

输出示例：
```
📁 目录内容: /path/to/directory
============================================================

📁 目录:
  📁 .git/
  📁 .venv/
  📁 src/

📄 文件:
  📄 README.md (2.5 KB) - 2024-06-10 18:30
  📄 requirements.txt (1.2 KB) - 2024-06-10 17:45
  📄 main.py (5.8 KB) - 2024-06-10 18:25

============================================================
📊 统计: 3 个目录, 3 个文件
```

### 示例 2: 显示隐藏文件
```json
{
  "function": "list_dir",
  "arguments": {
    "directory_path": ".",
    "show_hidden": true
  }
}
```

### 示例 3: 递归列出
```json
{
  "function": "list_dir",
  "arguments": {
    "directory_path": ".",
    "recursive": true,
    "max_depth": 2
  }
}
```

输出示例：
```
📁 目录内容: /path/to/directory
============================================================
📁 src/
  📄 main.py (3.2 KB) - 2024-06-10 18:20
  📄 utils.py (1.8 KB) - 2024-06-10 17:30
📁 tests/
  📄 test_main.py (2.1 KB) - 2024-06-10 18:15
📄 README.md (2.5 KB) - 2024-06-10 18:30
📄 requirements.txt (1.2 KB) - 2024-06-10 17:45

============================================================
📊 统计: 2 个目录, 5 个文件
```

## 工具定义

```json
{
  "type": "function",
  "function": {
    "name": "list_dir",
    "description": "列出指定目录的内容，包括文件和子目录的详细信息",
    "parameters": {
      "type": "object",
      "properties": {
        "directory_path": {
          "type": "string",
          "description": "要列出内容的目录路径（相对或绝对路径）"
        },
        "show_hidden": {
          "type": "boolean",
          "description": "是否显示隐藏文件和目录（默认为 false）"
        },
        "recursive": {
          "type": "boolean", 
          "description": "是否递归列出子目录内容（默认为 false）"
        },
        "max_depth": {
          "type": "integer",
          "description": "递归时的最大深度（默认为 2）"
        }
      },
      "required": ["directory_path"]
    }
  }
}
```

## 错误处理

函数会处理以下错误情况：
- 目录不存在
- 路径不是目录
- 权限不足
- 文件系统错误

错误示例：
```
错误：目录不存在: /nonexistent/path
错误：指定路径不是目录: /path/to/file.txt
错误：没有权限访问目录: /restricted/path
```

## 集成状态

✅ 工具定义已添加到 tools 数组
✅ list_directory() 函数已实现
✅ format_file_size() 辅助函数已添加
✅ 两个执行路径都已支持 list_dir
✅ System prompt 已更新包含目录操作说明

现在 DeepSeek Engineer 可以使用 list_dir 工具来浏览和分析目录结构！
