#!/usr/bin/env python3
"""
测试 list_dir 功能的简单脚本
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入需要的函数
try:
    from deepseek_eng import list_directory
except ImportError:
    # 如果导入失败，直接定义一个简单的测试函数
    def list_directory(path, show_hidden=False, recursive=False, max_depth=2):
        import os
        import datetime

        if not os.path.exists(path):
            return f"错误：目录不存在: {path}"

        result = [f"📁 目录内容: {path}", "=" * 60]

        try:
            items = os.listdir(path)
            if not show_hidden:
                items = [item for item in items if not item.startswith('.')]

            items.sort()
            dirs = []
            files = []

            for item in items:
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    dirs.append(item)
                else:
                    try:
                        size = os.path.getsize(item_path)
                        mtime = datetime.datetime.fromtimestamp(os.path.getmtime(item_path))
                        files.append((item, size, mtime.strftime("%Y-%m-%d %H:%M")))
                    except:
                        files.append((item, 0, "未知"))

            if dirs:
                result.append("\n📁 目录:")
                for d in dirs:
                    result.append(f"  📁 {d}/")

            if files:
                result.append("\n📄 文件:")
                for name, size, mtime in files:
                    result.append(f"  📄 {name} ({size} B) - {mtime}")

            result.append("=" * 60)
            result.append(f"📊 统计: {len(dirs)} 个目录, {len(files)} 个文件")

            return "\n".join(result)
        except Exception as e:
            return f"错误: {e}"

def test_list_directory():
    """测试目录列表功能"""
    print("🧪 测试 list_directory 功能")
    
    # 测试当前目录
    print("\n📁 测试 1: 列出当前目录")
    result = list_directory(".", show_hidden=False, recursive=False)
    print(result)
    
    print("\n" + "="*60)
    
    # 测试递归列出
    print("\n📁 测试 2: 递归列出当前目录（深度=1）")
    result = list_directory(".", show_hidden=False, recursive=True, max_depth=1)
    print(result)
    
    print("\n" + "="*60)
    
    # 测试显示隐藏文件
    print("\n📁 测试 3: 显示隐藏文件")
    result = list_directory(".", show_hidden=True, recursive=False)
    print(result)

if __name__ == "__main__":
    test_list_directory()
