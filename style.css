body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
    font-family: 'Arial', sans-serif;
}

.game-container {
    text-align: center;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(6, 60px);
    grid-gap: 5px;
    margin: 20px auto;
}

.tile {
    width: 60px;
    height: 60px;
    background-color: #3498db;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tile.selected {
    background-color: #e74c3c;
    transform: scale(0.95);
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.tile.removed {
    visibility: hidden;
    opacity: 0;
    transform: scale(0);
}

.tile.hint {
    background-color: #f1c40f;
    box-shadow: 0 0 15px #f1c40f;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.info-panel {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;
}

.info {
    font-size: 18px;
    font-weight: bold;
    padding: 5px 15px;
    background-color: #ecf0f1;
    border-radius: 5px;
}

.path-line {
    position: absolute;
    background-color: #e74c3c;
    z-index: 10;
}

.message {
    margin: 15px 0;
    font-size: 18px;
    font-weight: bold;
    height: 25px;
}

.btn {
    padding: 10px 20px;
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #27ae60;
}