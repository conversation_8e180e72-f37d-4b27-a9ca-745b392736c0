#!/usr/bin/env python3

import os
import sys
import json
import subprocess
from pathlib import Path
from textwrap import dedent
from typing import List, Dict, Any, Optional
from openai import OpenAI
from pydantic import BaseModel
from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.style import Style
from prompt_toolkit import PromptSession
from prompt_toolkit.styles import Style as PromptStyle
from prompt_toolkit.shortcuts import confirm
import time

# Initialize Rich console and prompt session
console = Console()
prompt_session = PromptSession(
    style=PromptStyle.from_dict({
        'prompt': '#0066ff bold',  # Bright blue prompt
        'completion-menu.completion': 'bg:#1e3a8a fg:#ffffff',
        'completion-menu.completion.current': 'bg:#3b82f6 fg:#ffffff bold',
    })
)

# 配置选项
SHOW_REASONING = False  # 设置为 True 可显示推理过程，False 则隐藏

# --------------------------------------------------------------------------------
# 1. Configure OpenAI client and load environment variables
# --------------------------------------------------------------------------------
load_dotenv()  # Load environment variables from .env file
client = OpenAI(
    api_key=os.getenv("DEEPSEEK_API_KEY"),
    base_url="https://api.deepseek.com"
)  # Configure for DeepSeek API

# --------------------------------------------------------------------------------
# 2. Define our schema using Pydantic for type safety
# --------------------------------------------------------------------------------
class FileToCreate(BaseModel):
    path: str
    content: str

class FileToEdit(BaseModel):
    path: str
    original_snippet: str
    new_snippet: str

# Remove AssistantResponse as we're using function calling now

# --------------------------------------------------------------------------------
# 2.1. Define Function Calling Tools
# --------------------------------------------------------------------------------
tools = [
    {
        "type": "function",
        "function": {
            "name": "read_file",
            "description": "Read the content of a single file from the filesystem",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path to the file to read (relative or absolute)",
                    }
                },
                "required": ["file_path"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "read_multiple_files",
            "description": "Read the content of multiple files from the filesystem",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_paths": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Array of file paths to read (relative or absolute)",
                    }
                },
                "required": ["file_paths"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_file",
            "description": "Create a new file or overwrite an existing file with the provided content",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path where the file should be created",
                    },
                    "content": {
                        "type": "string",
                        "description": "The content to write to the file",
                    }
                },
                "required": ["file_path", "content"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "create_multiple_files",
            "description": "Create multiple files at once",
            "parameters": {
                "type": "object",
                "properties": {
                    "files": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "path": {"type": "string"},
                                "content": {"type": "string"}
                            },
                            "required": ["path", "content"]
                        },
                        "description": "Array of files to create with their paths and content",
                    }
                },
                "required": ["files"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "edit_file",
            "description": "Edit an existing file by replacing a specific snippet with new content",
            "parameters": {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path to the file to edit",
                    },
                    "original_snippet": {
                        "type": "string",
                        "description": "The exact text snippet to find and replace",
                    },
                    "new_snippet": {
                        "type": "string",
                        "description": "The new text to replace the original snippet with",
                    }
                },
                "required": ["file_path", "original_snippet", "new_snippet"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "run_terminal_cmd",
            "description": "提议并在用户批准后运行终端命令。用于执行系统命令、安装依赖、运行脚本等操作。",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "要执行的终端命令",
                    },
                    "description": {
                        "type": "string",
                        "description": "对命令作用的简要说明，帮助用户理解命令的目的",
                    },
                    "working_directory": {
                        "type": "string",
                        "description": "执行命令的工作目录（可选，默认为当前目录）",
                    }
                },
                "required": ["command", "description"]
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "list_dir",
            "description": "列出指定目录的内容，包括文件和子目录的详细信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "directory_path": {
                        "type": "string",
                        "description": "要列出内容的目录路径（相对或绝对路径）",
                    },
                    "show_hidden": {
                        "type": "boolean",
                        "description": "是否显示隐藏文件和目录（默认为 false）",
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "是否递归列出子目录内容（默认为 false）",
                    },
                    "max_depth": {
                        "type": "integer",
                        "description": "递归时的最大深度（默认为 2）",
                    }
                },
                "required": ["directory_path"]
            },
        }
    }
]

# --------------------------------------------------------------------------------
# 3. system prompt
# --------------------------------------------------------------------------------
system_PROMPT = dedent("""\
    你是一个强大、具备智能体能力的 AI 编码助手，用中文进行沟通。
                       
    你正在与用户进行结对编程，以解决他们的编码任务。
    该任务可能需要创建新的代码库、修改或调试现有代码库，或者仅仅是回答一个问题。
    每次用户发送消息时，我们可能会自动附加一些关于他们当前状态的信息，例如他们打开了哪些文件、光标在哪里、最近查看的文件、到目前为止会话中的编辑历史、Linter 错误等等。
    这些信息可能与编码任务相关，也可能不相关，由你来决定。
    你的主要目标是在每条消息中遵循<user_query>标签中用户的指令。
                       
    <tool_calling>
    你有可用的工具来解决编码任务。关于工具调用，请遵循以下规则：
    1.  始终 (ALWAYS) 完全遵循指定的工具调用模式 (schema)，并确保提供所有必需的参数。
    2.  对话中可能引用不再可用的工具。绝不 (NEVER) 调用未明确提供的工具。
    3.  **在与用户交谈时，绝不 (NEVER) 提及工具名称。** 例如，不要说‘我需要使用 edit_file 工具来编辑你的文件’，而应该只说‘我将编辑你的文件’。
    4.  仅在必要时调用工具。如果用户的任务是通用的，或者你已经知道答案，只需直接回复，无需调用工具。
    5.  在调用每个工具之前，首先向用户解释你调用它的原因。
    </tool_calling>
                       
    <making_code_changes>
    进行代码更改时，除非被要求，否则绝不 (NEVER) 向用户输出代码。而是使用其中一个代码编辑工具来实现更改。
    每回合最多使用一次代码编辑工具。
    你生成的代码必须能够立即被用户运行，这一点极其重要。为确保这一点，请仔细遵循以下说明：
    1.  始终将对同一文件的编辑分组到单个 edit file 工具调用中，而不是多次调用。
    2.  如果从头开始创建代码库，请创建一个合适的依赖管理文件（例如 requirements.txt），包含包版本和一个有用的 README。
    3.  如果从头开始构建 Web 应用，请为其提供一个美观、现代化的 UI，并融入最佳的用户体验 (UX) 实践。
    4.  绝不 (NEVER) 生成极长的哈希或任何非文本代码，例如二进制文件。这些对用户没有帮助且成本高昂。
    5.  除非你是向文件追加一些小的、易于应用的编辑，或者创建新文件，否则你必须 (MUST) 在编辑之前读取你要编辑的内容或其所在部分。
    6.  如果你引入了 (linter) 错误，并且清楚如何修复（或者你能轻易弄清楚如何修复），则修复它们。不要做没有根据的猜测。并且，对于同一文件的 Linter 错误修复，循环尝试不要超过 3 次 (DO NOT loop more than 3 times)。第三次尝试时，你应该停下来询问用户接下来该怎么做。
    </making_code_changes>
                       
    <searching_and_reading>
    你有工具可以搜索代码库和读取文件。关于工具调用，请遵循以下规则：
    1.  如果可用，强烈倾向于 (heavily prefer) 使用语义搜索工具，而不是 grep 搜索、文件搜索和列出目录工具。
    2.  如果你需要读取文件，倾向于一次性读取文件的较大部分，而不是多次进行较小的调用。
    3.  如果你已经找到了进行编辑或回答的合理位置，不要继续调用工具。根据你已找到的信息进行编辑或回答。
</searching_and_reading>

    <terminal_commands>
    你可以使用 run_terminal_cmd 工具来执行系统命令：
    1. 用于安装依赖包、运行脚本、执行构建命令等
    2. 包含安全检查，会拒绝执行危险命令
    3. 需要用户确认才能执行，确保安全性
    4. 支持指定工作目录
    5. 在 Windows 上使用 PowerShell，在其他系统上使用 bash
    </terminal_commands>

    <directory_operations>
    你可以使用 list_dir 工具来浏览目录结构：
    1. 列出指定目录的文件和子目录
    2. 支持显示隐藏文件（show_hidden 参数）
    3. 支持递归列出子目录内容（recursive 参数）
    4. 可以控制递归深度（max_depth 参数）
    5. 显示文件大小和修改时间信息
    6. 提供目录和文件的统计信息
    </directory_operations>

    <code_style>
    写的代码要给上注释
    </code_style>
""")

# --------------------------------------------------------------------------------
# 4. Helper functions 
# --------------------------------------------------------------------------------

def read_local_file(file_path: str) -> str:
    """Return the text content of a local file."""
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()

def create_file(path: str, content: str):
    """Create (or overwrite) a file at 'path' with the given 'content'."""
    file_path = Path(path)
    
    # Security checks
    if any(part.startswith('~') for part in file_path.parts):
        raise ValueError("Home directory references not allowed")
    normalized_path = normalize_path(str(file_path))
    
    # Validate reasonable file size for operations
    if len(content) > 5_000_000:  # 5MB limit
        raise ValueError("File content exceeds 5MB size limit")
    
    file_path.parent.mkdir(parents=True, exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)
    console.print(f"[bold blue]✓[/bold blue] Created/updated file at '[bright_cyan]{file_path}[/bright_cyan]'")

def show_diff_table(files_to_edit: List[FileToEdit]) -> None:
    if not files_to_edit:
        return
    
    table = Table(title="📝 Proposed Edits", show_header=True, header_style="bold bright_blue", show_lines=True, border_style="blue")
    table.add_column("File Path", style="bright_cyan", no_wrap=True)
    table.add_column("Original", style="red dim")
    table.add_column("New", style="bright_green")

    for edit in files_to_edit:
        table.add_row(edit.path, edit.original_snippet, edit.new_snippet)
    
    console.print(table)

def apply_diff_edit(path: str, original_snippet: str, new_snippet: str):
    """Reads the file at 'path', replaces the first occurrence of 'original_snippet' with 'new_snippet', then overwrites."""
    try:
        content = read_local_file(path)
        
        # Verify we're replacing the exact intended occurrence
        occurrences = content.count(original_snippet)
        if occurrences == 0:
            raise ValueError("Original snippet not found")
        if occurrences > 1:
            console.print(f"[bold yellow]⚠ Multiple matches ({occurrences}) found - requiring line numbers for safety[/bold yellow]")
            console.print("[dim]Use format:\n--- original.py (lines X-Y)\n+++ modified.py[/dim]")
            raise ValueError(f"Ambiguous edit: {occurrences} matches")
        
        updated_content = content.replace(original_snippet, new_snippet, 1)
        create_file(path, updated_content)
        console.print(f"[bold blue]✓[/bold blue] Applied diff edit to '[bright_cyan]{path}[/bright_cyan]'")

    except FileNotFoundError:
        console.print(f"[bold red]✗[/bold red] File not found for diff editing: '[bright_cyan]{path}[/bright_cyan]'")
    except ValueError as e:
        console.print(f"[bold yellow]⚠[/bold yellow] {str(e)} in '[bright_cyan]{path}[/bright_cyan]'. No changes made.")
        console.print("\n[bold blue]Expected snippet:[/bold blue]")
        console.print(Panel(original_snippet, title="Expected", border_style="blue", title_align="left"))
        console.print("\n[bold blue]Actual file content:[/bold blue]")
        console.print(Panel(content, title="Actual", border_style="yellow", title_align="left"))

def try_handle_add_command(user_input: str) -> bool:
    prefix = "/add "
    if user_input.strip().lower().startswith(prefix):
        path_to_add = user_input[len(prefix):].strip()
        try:
            normalized_path = normalize_path(path_to_add)
            if os.path.isdir(normalized_path):
                # Handle entire directory
                add_directory_to_conversation(normalized_path)
            else:
                # Handle a single file as before
                content = read_local_file(normalized_path)
                conversation_history.append({
                    "role": "system",
                    "content": f"Content of file '{normalized_path}':\n\n{content}"
                })
                console.print(f"[bold blue]✓[/bold blue] Added file '[bright_cyan]{normalized_path}[/bright_cyan]' to conversation.\n")
        except OSError as e:
            console.print(f"[bold red]✗[/bold red] Could not add path '[bright_cyan]{path_to_add}[/bright_cyan]': {e}\n")
        return True
    return False

def add_directory_to_conversation(directory_path: str):
    with console.status("[bold bright_blue]🔍 Scanning directory...[/bold bright_blue]") as status:
        excluded_files = {
            # Python specific
            ".DS_Store", "Thumbs.db", ".gitignore", ".python-version",
            "uv.lock", ".uv", "uvenv", ".uvenv", ".venv", "venv",
            "__pycache__", ".pytest_cache", ".coverage", ".mypy_cache",
            # Node.js / Web specific
            "node_modules", "package-lock.json", "yarn.lock", "pnpm-lock.yaml",
            ".next", ".nuxt", "dist", "build", ".cache", ".parcel-cache",
            ".turbo", ".vercel", ".output", ".contentlayer",
            # Build outputs
            "out", "coverage", ".nyc_output", "storybook-static",
            # Environment and config
            ".env", ".env.local", ".env.development", ".env.production",
            # Misc
            ".git", ".svn", ".hg", "CVS"
        }
        excluded_extensions = {
            # Binary and media files
            ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".webp", ".avif",
            ".mp4", ".webm", ".mov", ".mp3", ".wav", ".ogg",
            ".zip", ".tar", ".gz", ".7z", ".rar",
            ".exe", ".dll", ".so", ".dylib", ".bin",
            # Documents
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            # Python specific
            ".pyc", ".pyo", ".pyd", ".egg", ".whl",
            # UV specific
            ".uv", ".uvenv",
            # Database and logs
            ".db", ".sqlite", ".sqlite3", ".log",
            # IDE specific
            ".idea", ".vscode",
            # Web specific
            ".map", ".chunk.js", ".chunk.css",
            ".min.js", ".min.css", ".bundle.js", ".bundle.css",
            # Cache and temp files
            ".cache", ".tmp", ".temp",
            # Font files
            ".ttf", ".otf", ".woff", ".woff2", ".eot"
        }
        skipped_files = []
        added_files = []
        total_files_processed = 0
        max_files = 1000  # Reasonable limit for files to process
        max_file_size = 5_000_000  # 5MB limit

        for root, dirs, files in os.walk(directory_path):
            if total_files_processed >= max_files:
                console.print(f"[bold yellow]⚠[/bold yellow] Reached maximum file limit ({max_files})")
                break

            status.update(f"[bold bright_blue]🔍 Scanning {root}...[/bold bright_blue]")
            # Skip hidden directories and excluded directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in excluded_files]

            for file in files:
                if total_files_processed >= max_files:
                    break

                if file.startswith('.') or file in excluded_files:
                    skipped_files.append(os.path.join(root, file))
                    continue

                _, ext = os.path.splitext(file)
                if ext.lower() in excluded_extensions:
                    skipped_files.append(os.path.join(root, file))
                    continue

                full_path = os.path.join(root, file)

                try:
                    # Check file size before processing
                    if os.path.getsize(full_path) > max_file_size:
                        skipped_files.append(f"{full_path} (exceeds size limit)")
                        continue

                    # Check if it's binary
                    if is_binary_file(full_path):
                        skipped_files.append(full_path)
                        continue

                    normalized_path = normalize_path(full_path)
                    content = read_local_file(normalized_path)
                    conversation_history.append({
                        "role": "system",
                        "content": f"Content of file '{normalized_path}':\n\n{content}"
                    })
                    added_files.append(normalized_path)
                    total_files_processed += 1

                except OSError:
                    skipped_files.append(full_path)

        console.print(f"[bold blue]✓[/bold blue] Added folder '[bright_cyan]{directory_path}[/bright_cyan]' to conversation.")
        if added_files:
            console.print(f"\n[bold bright_blue]📁 Added files:[/bold bright_blue] [dim]({len(added_files)} of {total_files_processed})[/dim]")
            for f in added_files:
                console.print(f"  [bright_cyan]📄 {f}[/bright_cyan]")
        if skipped_files:
            console.print(f"\n[bold yellow]⏭ Skipped files:[/bold yellow] [dim]({len(skipped_files)})[/dim]")
            for f in skipped_files[:10]:  # Show only first 10 to avoid clutter
                console.print(f"  [yellow dim]⚠ {f}[/yellow dim]")
            if len(skipped_files) > 10:
                console.print(f"  [dim]... and {len(skipped_files) - 10} more[/dim]")
        console.print()

def is_binary_file(file_path: str, peek_size: int = 1024) -> bool:
    try:
        with open(file_path, 'rb') as f:
            chunk = f.read(peek_size)
        # If there is a null byte in the sample, treat it as binary
        if b'\0' in chunk:
            return True
        return False
    except Exception:
        # If we fail to read, just treat it as binary to be safe
        return True

def ensure_file_in_context(file_path: str) -> bool:
    try:
        normalized_path = normalize_path(file_path)
        content = read_local_file(normalized_path)
        file_marker = f"Content of file '{normalized_path}'"
        if not any(file_marker in msg["content"] for msg in conversation_history):
            conversation_history.append({
                "role": "system",
                "content": f"{file_marker}:\n\n{content}"
            })
        return True
    except OSError:
        console.print(f"[bold red]✗[/bold red] Could not read file '[bright_cyan]{file_path}[/bright_cyan]' for editing context")
        return False

def normalize_path(path_str: str) -> str:
    """Return a canonical, absolute version of the path with security checks."""
    path = Path(path_str).resolve()

    # Prevent directory traversal attacks
    if ".." in path.parts:
        raise ValueError(f"Invalid path: {path_str} contains parent directory references")

    return str(path)

def run_terminal_command(command: str, description: str, working_directory: str = None) -> str:
    """提议并在用户批准后运行终端命令"""
    try:
        # 显示命令提议
        console.print(f"\n[bold bright_blue]💻 终端命令提议[/bold bright_blue]")
        console.print(f"[bold yellow]描述：[/bold yellow] {description}")
        console.print(f"[bold yellow]命令：[/bold yellow] [bright_cyan]{command}[/bright_cyan]")

        if working_directory:
            console.print(f"[bold yellow]工作目录：[/bold yellow] [bright_cyan]{working_directory}[/bright_cyan]")

        # 安全检查 - 禁止危险命令
        dangerous_commands = [
            'rm -rf', 'del /f', 'format', 'fdisk', 'mkfs',
            'shutdown', 'reboot', 'halt', 'poweroff',
            'dd if=', 'chmod 777', 'chown -R',
            'sudo rm', 'sudo del', '> /dev/', 'curl | sh', 'wget | sh'
        ]

        command_lower = command.lower()
        for dangerous in dangerous_commands:
            if dangerous in command_lower:
                return f"错误：检测到潜在危险命令 '{dangerous}'，已拒绝执行"

        # 询问用户确认
        console.print(f"\n[bold red]⚠️  警告：即将执行系统命令[/bold red]")

        try:
            user_confirmed = confirm(
                "是否确认执行此命令？(y/n): ",
                default=False
            )
        except (EOFError, KeyboardInterrupt):
            return "用户取消了命令执行"

        if not user_confirmed:
            return "用户拒绝执行命令"

        # 执行命令
        console.print(f"\n[bold bright_green]🚀 正在执行命令...[/bold bright_green]")

        # 设置工作目录
        cwd = working_directory if working_directory else os.getcwd()
        if working_directory:
            # 验证工作目录存在
            if not os.path.exists(working_directory):
                return f"错误：工作目录不存在: {working_directory}"

        # 在 Windows 上使用 PowerShell
        if os.name == 'nt':  # Windows
            full_command = ['powershell', '-Command', command]
        else:  # Unix/Linux/Mac
            full_command = ['bash', '-c', command]

        # 执行命令
        result = subprocess.run(
            full_command,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            encoding='utf-8',
            errors='replace'
        )

        # 格式化输出
        output_lines = []
        output_lines.append(f"命令: {command}")
        output_lines.append(f"工作目录: {cwd}")
        output_lines.append(f"退出代码: {result.returncode}")

        if result.stdout:
            output_lines.append(f"\n标准输出:\n{result.stdout}")

        if result.stderr:
            output_lines.append(f"\n标准错误:\n{result.stderr}")

        # 显示结果
        if result.returncode == 0:
            console.print(f"[bold green]✅ 命令执行成功[/bold green]")
        else:
            console.print(f"[bold red]❌ 命令执行失败 (退出代码: {result.returncode})[/bold red]")

        return "\n".join(output_lines)

    except subprocess.TimeoutExpired:
        return f"错误：命令执行超时 (超过5分钟): {command}"
    except subprocess.CalledProcessError as e:
        return f"错误：命令执行失败: {e}"
    except Exception as e:
        return f"错误：执行命令时发生异常: {str(e)}"

def list_directory(directory_path: str, show_hidden: bool = False, recursive: bool = False, max_depth: int = 2) -> str:
    """列出指定目录的内容"""
    try:
        # 标准化路径
        normalized_path = normalize_path(directory_path)

        # 检查目录是否存在
        if not os.path.exists(normalized_path):
            return f"错误：目录不存在: {directory_path}"

        if not os.path.isdir(normalized_path):
            return f"错误：指定路径不是目录: {directory_path}"

        result_lines = []
        result_lines.append(f"📁 目录内容: {normalized_path}")
        result_lines.append("=" * 60)

        if recursive:
            # 递归列出目录内容
            total_files = 0
            total_dirs = 0

            for root, dirs, files in os.walk(normalized_path):
                # 计算当前深度
                current_depth = root.replace(normalized_path, '').count(os.sep)
                if current_depth >= max_depth:
                    dirs.clear()  # 不再深入子目录
                    continue

                # 过滤隐藏目录
                if not show_hidden:
                    dirs[:] = [d for d in dirs if not d.startswith('.')]

                # 显示当前目录
                indent = "  " * current_depth
                rel_path = os.path.relpath(root, normalized_path)
                if rel_path != ".":
                    result_lines.append(f"{indent}📁 {rel_path}/")

                # 显示文件
                visible_files = files if show_hidden else [f for f in files if not f.startswith('.')]
                for file in sorted(visible_files):
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        file_size_str = format_file_size(file_size)

                        # 获取文件修改时间
                        import datetime
                        mtime = os.path.getmtime(file_path)
                        mtime_str = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M")

                        result_lines.append(f"{indent}  📄 {file} ({file_size_str}) - {mtime_str}")
                        total_files += 1
                    except OSError:
                        result_lines.append(f"{indent}  📄 {file} (无法访问)")

                total_dirs += len([d for d in dirs if show_hidden or not d.startswith('.')])

            result_lines.append("=" * 60)
            result_lines.append(f"📊 统计: {total_dirs} 个目录, {total_files} 个文件")

        else:
            # 非递归，只列出当前目录
            try:
                items = os.listdir(normalized_path)
                if not show_hidden:
                    items = [item for item in items if not item.startswith('.')]

                items.sort()

                dirs = []
                files = []

                for item in items:
                    item_path = os.path.join(normalized_path, item)
                    try:
                        if os.path.isdir(item_path):
                            dirs.append(item)
                        else:
                            file_size = os.path.getsize(item_path)
                            file_size_str = format_file_size(file_size)

                            # 获取文件修改时间
                            import datetime
                            mtime = os.path.getmtime(item_path)
                            mtime_str = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M")

                            files.append((item, file_size_str, mtime_str))
                    except OSError:
                        files.append((item, "无法访问", "未知"))

                # 显示目录
                if dirs:
                    result_lines.append("\n📁 目录:")
                    for dir_name in dirs:
                        result_lines.append(f"  📁 {dir_name}/")

                # 显示文件
                if files:
                    result_lines.append("\n📄 文件:")
                    for file_name, size_str, mtime_str in files:
                        result_lines.append(f"  📄 {file_name} ({size_str}) - {mtime_str}")

                result_lines.append("=" * 60)
                result_lines.append(f"📊 统计: {len(dirs)} 个目录, {len(files)} 个文件")

            except PermissionError:
                return f"错误：没有权限访问目录: {directory_path}"
            except OSError as e:
                return f"错误：无法读取目录: {e}"

        return "\n".join(result_lines)

    except Exception as e:
        return f"错误：列出目录内容时发生异常: {str(e)}"

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

# --------------------------------------------------------------------------------
# 5. Conversation state
# --------------------------------------------------------------------------------
conversation_history = [
    {"role": "system", "content": system_PROMPT}
]

# --------------------------------------------------------------------------------
# 6. Message validation and OpenAI API interaction with streaming
# --------------------------------------------------------------------------------

def validate_conversation_history():
    """Validate that conversation history follows OpenAI API format rules."""
    for i, message in enumerate(conversation_history):
        if message.get("role") == "assistant" and message.get("tool_calls"):
            # Check if this assistant message with tool_calls is followed by tool responses
            tool_call_ids = [tc["id"] for tc in message["tool_calls"]]

            # Look for corresponding tool responses
            j = i + 1
            found_tool_responses = set()

            while j < len(conversation_history) and conversation_history[j].get("role") == "tool":
                found_tool_responses.add(conversation_history[j].get("tool_call_id"))
                j += 1

            # Check if all tool_call_ids have corresponding responses
            missing_responses = set(tool_call_ids) - found_tool_responses
            if missing_responses:
                console.print(f"[red]Warning: Missing tool responses for IDs: {missing_responses}[/red]")
                return False

    return True

def debug_conversation_history():
    """Print conversation history for debugging."""
    console.print("\n[bold yellow]🔍 Debug: Conversation History[/bold yellow]")
    for i, message in enumerate(conversation_history[-5:]):  # Show last 5 messages
        role = message.get("role", "unknown")
        content = message.get("content", "")
        tool_calls = message.get("tool_calls", [])
        tool_call_id = message.get("tool_call_id", "")

        console.print(f"[dim]{i}: {role}[/dim]")
        if content:
            console.print(f"  Content: {content[:100]}...")
        if tool_calls:
            console.print(f"  Tool calls: {len(tool_calls)}")
            for tc in tool_calls:
                console.print(f"    - {tc.get('function', {}).get('name', 'unknown')} (ID: {tc.get('id', 'missing')})")
        if tool_call_id:
            console.print(f"  Tool call ID: {tool_call_id}")
        console.print()

# --------------------------------------------------------------------------------
# 6.1. OpenAI API interaction with streaming
# --------------------------------------------------------------------------------

def execute_function_call_dict(tool_call_dict) -> str:
    """Execute a function call from a dictionary format and return the result as a string."""
    try:
        function_name = tool_call_dict["function"]["name"]
        arguments = json.loads(tool_call_dict["function"]["arguments"])
        
        if function_name == "read_file":
            file_path = arguments["file_path"]
            normalized_path = normalize_path(file_path)
            content = read_local_file(normalized_path)
            return f"Content of file '{normalized_path}':\n\n{content}"
            
        elif function_name == "read_multiple_files":
            file_paths = arguments["file_paths"]
            results = []
            for file_path in file_paths:
                try:
                    normalized_path = normalize_path(file_path)
                    content = read_local_file(normalized_path)
                    results.append(f"Content of file '{normalized_path}':\n\n{content}")
                except OSError as e:
                    results.append(f"Error reading '{file_path}': {e}")
            return "\n\n" + "="*50 + "\n\n".join(results)
            
        elif function_name == "create_file":
            file_path = arguments["file_path"]
            content = arguments["content"]
            create_file(file_path, content)
            return f"Successfully created file '{file_path}'"
            
        elif function_name == "create_multiple_files":
            files = arguments["files"]
            created_files = []
            for file_info in files:
                create_file(file_info["path"], file_info["content"])
                created_files.append(file_info["path"])
            return f"Successfully created {len(created_files)} files: {', '.join(created_files)}"
            
        elif function_name == "edit_file":
            file_path = arguments["file_path"]
            original_snippet = arguments["original_snippet"]
            new_snippet = arguments["new_snippet"]

            # Ensure file is in context first
            if not ensure_file_in_context(file_path):
                return f"Error: Could not read file '{file_path}' for editing"

            apply_diff_edit(file_path, original_snippet, new_snippet)
            return f"Successfully edited file '{file_path}'"

        elif function_name == "run_terminal_cmd":
            command = arguments["command"]
            description = arguments["description"]
            working_directory = arguments.get("working_directory")

            return run_terminal_command(command, description, working_directory)

        elif function_name == "list_dir":
            directory_path = arguments["directory_path"]
            show_hidden = arguments.get("show_hidden", False)
            recursive = arguments.get("recursive", False)
            max_depth = arguments.get("max_depth", 2)

            return list_directory(directory_path, show_hidden, recursive, max_depth)

        else:
            return f"Unknown function: {function_name}"
            
    except Exception as e:
        return f"Error executing {function_name}: {str(e)}"

def execute_function_call(tool_call) -> str:
    """Execute a function call and return the result as a string."""
    try:
        function_name = tool_call.function.name
        arguments = json.loads(tool_call.function.arguments)
        
        if function_name == "read_file":
            file_path = arguments["file_path"]
            normalized_path = normalize_path(file_path)
            content = read_local_file(normalized_path)
            return f"Content of file '{normalized_path}':\n\n{content}"
            
        elif function_name == "read_multiple_files":
            file_paths = arguments["file_paths"]
            results = []
            for file_path in file_paths:
                try:
                    normalized_path = normalize_path(file_path)
                    content = read_local_file(normalized_path)
                    results.append(f"Content of file '{normalized_path}':\n\n{content}")
                except OSError as e:
                    results.append(f"Error reading '{file_path}': {e}")
            return "\n\n" + "="*50 + "\n\n".join(results)
            
        elif function_name == "create_file":
            file_path = arguments["file_path"]
            content = arguments["content"]
            create_file(file_path, content)
            return f"Successfully created file '{file_path}'"
            
        elif function_name == "create_multiple_files":
            files = arguments["files"]
            created_files = []
            for file_info in files:
                create_file(file_info["path"], file_info["content"])
                created_files.append(file_info["path"])
            return f"Successfully created {len(created_files)} files: {', '.join(created_files)}"
            
        elif function_name == "edit_file":
            file_path = arguments["file_path"]
            original_snippet = arguments["original_snippet"]
            new_snippet = arguments["new_snippet"]

            # Ensure file is in context first
            if not ensure_file_in_context(file_path):
                return f"Error: Could not read file '{file_path}' for editing"

            apply_diff_edit(file_path, original_snippet, new_snippet)
            return f"Successfully edited file '{file_path}'"

        elif function_name == "run_terminal_cmd":
            command = arguments["command"]
            description = arguments["description"]
            working_directory = arguments.get("working_directory")

            return run_terminal_command(command, description, working_directory)

        elif function_name == "list_dir":
            directory_path = arguments["directory_path"]
            show_hidden = arguments.get("show_hidden", False)
            recursive = arguments.get("recursive", False)
            max_depth = arguments.get("max_depth", 2)

            return list_directory(directory_path, show_hidden, recursive, max_depth)

        else:
            return f"Unknown function: {function_name}"
            
    except Exception as e:
        return f"Error executing {function_name}: {str(e)}"

def trim_conversation_history():
    """Trim conversation history to prevent token limit issues while preserving tool call sequences"""
    if len(conversation_history) <= 20:  # Don't trim if conversation is still small
        return
        
    # Always keep the system prompt
    system_msgs = [msg for msg in conversation_history if msg["role"] == "system"]
    other_msgs = [msg for msg in conversation_history if msg["role"] != "system"]
    
    # Keep only the last 15 messages to prevent token overflow
    if len(other_msgs) > 15:
        other_msgs = other_msgs[-15:]
    
    # Rebuild conversation history
    conversation_history.clear()
    conversation_history.extend(system_msgs + other_msgs)

def stream_openai_response(user_message: str):
    # Add the user message to conversation history
    conversation_history.append({"role": "user", "content": user_message})
    
    # Trim conversation history if it's getting too long
    trim_conversation_history()

    # Validate conversation history before making API call
    if not validate_conversation_history():
        console.print("[red]Conversation history validation failed. Attempting to fix...[/red]")
        # Could add auto-fix logic here if needed

    # Remove the old file guessing logic since we'll use function calls
    try:
        stream = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=conversation_history,
            tools=tools,
            max_completion_tokens=64000,
            stream=True
        )

        console.print("\n[bold bright_blue]🐋 Seeking...[/bold bright_blue]")
        reasoning_started = False
        reasoning_content = ""
        final_content = ""
        tool_calls = []

        for chunk in stream:
            # Handle reasoning content if available
            if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                reasoning_content += chunk.choices[0].delta.reasoning_content
                if not reasoning_started:
                    reasoning_started = True
                    if SHOW_REASONING:
                        console.print("\n[bold blue]💭 Reasoning:[/bold blue]")

                # 只有在配置允许时才显示 reasoning
                if SHOW_REASONING:
                    console.print(chunk.choices[0].delta.reasoning_content, end="")
            elif chunk.choices[0].delta.content:
                if reasoning_started:
                    # 第一次显示内容时显示 Assistant 标识
                    if SHOW_REASONING:
                        console.print("\n")  # 如果显示了 reasoning，需要换行
                    console.print("\n[bold bright_blue]🤖 Assistant>[/bold bright_blue] ", end="")
                    reasoning_started = False
                final_content += chunk.choices[0].delta.content
                console.print(chunk.choices[0].delta.content, end="")
            elif chunk.choices[0].delta.tool_calls:
                # Handle tool calls
                for tool_call_delta in chunk.choices[0].delta.tool_calls:
                    if tool_call_delta.index is not None:
                        # Ensure we have enough tool_calls
                        while len(tool_calls) <= tool_call_delta.index:
                            tool_calls.append({
                                "id": "",
                                "type": "function",
                                "function": {"name": "", "arguments": ""}
                            })
                        
                        if tool_call_delta.id:
                            tool_calls[tool_call_delta.index]["id"] = tool_call_delta.id
                        if tool_call_delta.function:
                            if tool_call_delta.function.name:
                                tool_calls[tool_call_delta.index]["function"]["name"] += tool_call_delta.function.name
                            if tool_call_delta.function.arguments:
                                tool_calls[tool_call_delta.index]["function"]["arguments"] += tool_call_delta.function.arguments

        console.print()  # New line after streaming

        # Store the assistant's response in conversation history
        assistant_message = {
            "role": "assistant",
            "content": final_content if final_content else None
        }
        
        if tool_calls:
            # Convert our tool_calls format to the expected format
            formatted_tool_calls = []
            for i, tc in enumerate(tool_calls):
                # Validate tool call has required fields
                if tc.get("function", {}).get("name") and tc.get("function", {}).get("arguments"):
                    # Ensure we have a valid tool call ID
                    tool_id = tc.get("id")
                    if not tool_id:
                        tool_id = f"call_{i}_{int(time.time() * 1000)}"

                    # Validate arguments is valid JSON
                    try:
                        json.loads(tc["function"]["arguments"])
                        formatted_tool_calls.append({
                            "id": tool_id,
                            "type": "function",
                            "function": {
                                "name": tc["function"]["name"],
                                "arguments": tc["function"]["arguments"]
                            }
                        })
                    except json.JSONDecodeError:
                        console.print(f"[red]Warning: Invalid JSON in tool call arguments for {tc['function']['name']}[/red]")
                        continue

            if formatted_tool_calls:
                # Important: When there are tool calls, content should be None or empty
                assistant_message["content"] = None  # Always set to None when tool_calls exist
                assistant_message["tool_calls"] = formatted_tool_calls
                conversation_history.append(assistant_message)

                # Execute tool calls and add results immediately
                console.print(f"\n[bold bright_cyan]⚡ Executing {len(formatted_tool_calls)} function call(s)...[/bold bright_cyan]")

                # Collect all tool responses before adding to conversation
                tool_responses = []
                for tool_call in formatted_tool_calls:
                    console.print(f"[bright_blue]→ {tool_call['function']['name']}[/bright_blue]")

                    try:
                        result = execute_function_call_dict(tool_call)
                        tool_response = {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": result
                        }
                        tool_responses.append(tool_response)
                    except Exception as e:
                        console.print(f"[red]Error executing {tool_call['function']['name']}: {e}[/red]")
                        # Still need to add a tool response even on error
                        tool_response = {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": f"Error: {str(e)}"
                        }
                        tool_responses.append(tool_response)

                # Add all tool responses to conversation history
                conversation_history.extend(tool_responses)
                
                # Get follow-up response after tool execution
                console.print("\n[bold bright_blue]🔄 Processing results...[/bold bright_blue]")

                # Validate conversation history before follow-up call
                if not validate_conversation_history():
                    console.print("[red]Warning: Conversation history validation failed before follow-up[/red]")

                follow_up_stream = client.chat.completions.create(
                    model="deepseek-reasoner",
                    messages=conversation_history,
                    tools=tools,
                    max_completion_tokens=64000,
                    stream=True
                )
                
                follow_up_content = ""
                reasoning_started = False
                
                for chunk in follow_up_stream:
                    # Handle reasoning content if available
                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                        if not reasoning_started:
                            reasoning_started = True
                            if SHOW_REASONING:
                                console.print("\n[bold blue]💭 Reasoning:[/bold blue]")

                        # 只有在配置允许时才显示 reasoning
                        if SHOW_REASONING:
                            console.print(chunk.choices[0].delta.reasoning_content, end="")
                    elif chunk.choices[0].delta.content:
                        if reasoning_started:
                            # 第一次显示内容时显示 Assistant 标识
                            if SHOW_REASONING:
                                console.print("\n")  # 如果显示了 reasoning，需要换行
                            console.print("\n[bold bright_blue]🤖 Assistant>[/bold bright_blue] ", end="")
                            reasoning_started = False
                        follow_up_content += chunk.choices[0].delta.content
                        console.print(chunk.choices[0].delta.content, end="")
                
                console.print()
                
                # Store follow-up response
                conversation_history.append({
                    "role": "assistant",
                    "content": follow_up_content
                })
        else:
            # No tool calls, just store the regular response
            conversation_history.append(assistant_message)

        return {"success": True}

    except Exception as e:
        error_msg = f"DeepSeek API error: {str(e)}"
        console.print(f"\n[bold red]❌ {error_msg}[/bold red]")

        # If it's a tool_calls related error, show debug info
        if "tool_calls" in str(e).lower() or "tool messages" in str(e).lower():
            console.print("[yellow]Showing conversation history for debugging:[/yellow]")
            debug_conversation_history()

        return {"error": error_msg}

# --------------------------------------------------------------------------------
# 7. Main interactive loop
# --------------------------------------------------------------------------------

def main():
    global SHOW_REASONING

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--show-reasoning":
        SHOW_REASONING = True
        console.print("[bold yellow]💭 推理过程显示已启用[/bold yellow]")

    # Create a beautiful gradient-style welcome panel
    reasoning_status = "显示推理过程" if SHOW_REASONING else "隐藏推理过程"
    welcome_text = f"""[bold bright_blue]🐋 DeepSeek Engineer[/bold bright_blue] [bright_cyan]with Function Calling[/bright_cyan]
[dim blue]Powered by DeepSeek-R1 with Chain-of-Thought Reasoning[/dim blue]
[dim green]当前设置: {reasoning_status}[/dim green]"""
    
    console.print(Panel.fit(
        welcome_text,
        border_style="bright_blue",
        padding=(1, 2),
        title="[bold bright_cyan]🤖 AI Code Assistant[/bold bright_cyan]",
        title_align="center"
    ))
    
    # Create an elegant instruction panel
    instructions = f"""[bold bright_blue]📁 File Operations:[/bold bright_blue]
  • [bright_cyan]/add path/to/file[/bright_cyan] - Include a single file in conversation
  • [bright_cyan]/add path/to/folder[/bright_cyan] - Include all files in a folder
  • [dim]The AI can automatically read and create files using function calls[/dim]

[bold bright_blue]🎯 Commands:[/bold bright_blue]
  • [bright_cyan]exit[/bright_cyan] or [bright_cyan]quit[/bright_cyan] - End the session
  • Just ask naturally - the AI will handle file operations automatically!

[bold bright_blue]⚙️ Settings:[/bold bright_blue]
  • 推理过程: {'显示' if SHOW_REASONING else '隐藏'} (使用 --show-reasoning 参数启用)"""
    
    console.print(Panel(
        instructions,
        border_style="blue",
        padding=(1, 2),
        title="[bold blue]💡 How to Use[/bold blue]",
        title_align="left"
    ))
    console.print()

    while True:
        try:
            user_input = prompt_session.prompt("🔵 You> ").strip()
        except (EOFError, KeyboardInterrupt):
            console.print("\n[bold yellow]👋 Exiting gracefully...[/bold yellow]")
            break

        if not user_input:
            continue

        if user_input.lower() in ["exit", "quit"]:
            console.print("[bold bright_blue]👋 Goodbye! Happy coding![/bold bright_blue]")
            break

        if try_handle_add_command(user_input):
            continue

        response_data = stream_openai_response(user_input)
        
        if response_data.get("error"):
            console.print(f"[bold red]❌ Error: {response_data['error']}[/bold red]")

    console.print("[bold blue]✨ Session finished. Thank you for using DeepSeek Engineer![/bold blue]")

if __name__ == "__main__":
    main()