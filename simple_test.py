#!/usr/bin/env python3
"""
简单测试 list_directory 功能
"""

import os
import datetime

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def list_directory_simple(directory_path: str) -> str:
    """简单的目录列表功能"""
    try:
        if not os.path.exists(directory_path):
            return f"错误：目录不存在: {directory_path}"
        
        if not os.path.isdir(directory_path):
            return f"错误：指定路径不是目录: {directory_path}"
        
        result_lines = []
        result_lines.append(f"📁 目录内容: {directory_path}")
        result_lines.append("=" * 60)
        
        items = os.listdir(directory_path)
        items = [item for item in items if not item.startswith('.')]  # 隐藏文件
        items.sort()
        
        dirs = []
        files = []
        
        for item in items:
            item_path = os.path.join(directory_path, item)
            try:
                if os.path.isdir(item_path):
                    dirs.append(item)
                else:
                    file_size = os.path.getsize(item_path)
                    file_size_str = format_file_size(file_size)
                    
                    mtime = os.path.getmtime(item_path)
                    mtime_str = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M")
                    
                    files.append((item, file_size_str, mtime_str))
            except OSError:
                files.append((item, "无法访问", "未知"))
        
        # 显示目录
        if dirs:
            result_lines.append("\n📁 目录:")
            for dir_name in dirs:
                result_lines.append(f"  📁 {dir_name}/")
        
        # 显示文件
        if files:
            result_lines.append("\n📄 文件:")
            for file_name, size_str, mtime_str in files:
                result_lines.append(f"  📄 {file_name} ({size_str}) - {mtime_str}")
        
        result_lines.append("=" * 60)
        result_lines.append(f"📊 统计: {len(dirs)} 个目录, {len(files)} 个文件")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"错误：列出目录内容时发生异常: {str(e)}"

if __name__ == "__main__":
    print("🧪 测试目录列表功能")
    result = list_directory_simple(".")
    print(result)
