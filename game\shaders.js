// 顶点着色器源码
const vertexShaderSource = `
    attribute vec3 aPosition;
    attribute vec2 aTexCoord;
    
    uniform mat4 uModel;
    uniform mat4 uView;
    uniform mat4 uProjection;
    
    varying vec2 vTexCoord;
    
    void main() {
        gl_Position = uProjection * uView * uModel * vec4(aPosition, 1.0);
        vTexCoord = aTexCoord;
    }
`;

// 片段着色器源码
const fragmentShaderSource = `
    precision mediump float;
    
    varying vec2 vTexCoord;
    uniform sampler2D uTexture;
    uniform vec2 uTexOffset;
    
    void main() {
        vec2 texCoord = vTexCoord * 0.25 + uTexOffset;
        gl_FragColor = texture2D(uTexture, texCoord);
        
        // 添加发光效果
        float intensity = 0.5 + 0.5 * sin(gl_FragCoord.x * 0.1 + gl_FragCoord.y * 0.1);
        gl_FragColor.rgb *= 1.0 + 0.3 * intensity;
    }
`;