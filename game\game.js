// 游戏常量
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 8;
const TILE_TYPES = 12;
const TILE_SIZE = 60;

// 游戏状态
let gameBoard = [];
let selectedTile = null;
let score = 0;
let timeLeft = 120;
let gameActive = true;

// WebGL变量
let gl;
let shaderProgram;
let vertexBuffer;
let indexBuffer;
let texture;

// 初始化游戏
function initGame() {
    createBoard();
    setupEventListeners();
    initWebGL();
    initShaders();
    initBuffers();
    initTexture();
    gameLoop();
    startTimer();
}

// 创建游戏板
function createBoard() {
    gameBoard = [];
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        gameBoard[y] = [];
        for (let x = 0; x < BOARD_WIDTH; x++) {
            gameBoard[y][x] = Math.floor(Math.random() * TILE_TYPES);
        }
    }
    
    // 确保棋盘有可解性
    while (!isSolvable()) {
        createBoard();
    }
}

// 检查棋盘是否可解
function isSolvable() {
    // 简化版的可解性检查
    let countMap = {};
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            const type = gameBoard[y][x];
            countMap[type] = (countMap[type] || 0) + 1;
        }
    }
    
    // 检查每种类型的数量是否为偶数
    for (const type in countMap) {
        if (countMap[type] % 2 !== 0) return false;
    }
    return true;
}

// 设置事件监听器
function setupEventListeners() {
    const canvas = document.getElementById('gameCanvas');
    canvas.addEventListener('click', handleCanvasClick);
    
    document.getElementById('restart').addEventListener('click', () => {
        score = 0;
        timeLeft = 120;
        gameActive = true;
        document.getElementById('score').textContent = score;
        document.getElementById('timer').textContent = timeLeft;
        createBoard();
    });
    
    document.getElementById('hint').addEventListener('click', showHint);
}

// 处理画布点击
function handleCanvasClick(event) {
    if (!gameActive) return;
    
    const canvas = document.getElementById('gameCanvas');
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const tileX = Math.floor(x / TILE_SIZE);
    const tileY = Math.floor(y / TILE_SIZE);
    
    if (tileX >= 0 && tileX < BOARD_WIDTH && tileY >= 0 && tileY < BOARD_HEIGHT) {
        if (selectedTile) {
            // 尝试连接两个方块
            if (canConnect(selectedTile, {x: tileX, y: tileY})) {
                removeTiles(selectedTile, {x: tileX, y: tileY});
                score += 10;
                document.getElementById('score').textContent = score;
                selectedTile = null;
            } else {
                selectedTile = {x: tileX, y: tileY};
            }
        } else {
            selectedTile = {x: tileX, y: tileY};
        }
    }
}

// 检查两个方块是否可以连接
function canConnect(tile1, tile2) {
    if (tile1.x === tile2.x && tile1.y === tile2.y) return false;
    if (gameBoard[tile1.y][tile1.x] !== gameBoard[tile2.y][tile2.x]) return false;
    
    // 简化版的路径检查
    return (
        checkHorizontal(tile1, tile2) || 
        checkVertical(tile1, tile2) ||
        checkOneCorner(tile1, tile2) ||
        checkTwoCorners(tile1, tile2)
    );
}

// 移除方块
function removeTiles(tile1, tile2) {
    gameBoard[tile1.y][tile1.x] = -1;
    gameBoard[tile2.y][tile2.x] = -1;
}

// 显示提示
function showHint() {
    // 简化版的提示功能
    for (let y1 = 0; y1 < BOARD_HEIGHT; y1++) {
        for (let x1 = 0; x1 < BOARD_WIDTH; x1++) {
            if (gameBoard[y1][x1] === -1) continue;
            
            for (let y2 = 0; y2 < BOARD_HEIGHT; y2++) {
                for (let x2 = 0; x2 < BOARD_WIDTH; x2++) {
                    if ((x1 === x2 && y1 === y2) || gameBoard[y2][x2] === -1) continue;
                    
                    if (canConnect({x: x1, y: y1}, {x: x2, y: y2})) {
                        // 高亮显示可连接的两个方块
                        selectedTile = {x: x1, y: y1};
                        return;
                    }
                }
            }
        }
    }
}

// 初始化WebGL
function initWebGL() {
    const canvas = document.getElementById('gameCanvas');
    gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) {
        alert('您的浏览器不支持WebGL');
        return;
    }
    
    gl.viewport(0, 0, canvas.width, canvas.height);
    gl.clearColor(0.0, 0.0, 0.0, 1.0);
}

// 初始化着色器
function initShaders() {
    shaderProgram = gl.createProgram();
    
    // 顶点着色器
    const vertexShader = gl.createShader(gl.VERTEX_SHADER);
    gl.shaderSource(vertexShader, vertexShaderSource);
    gl.compileShader(vertexShader);
    
    // 片段着色器
    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
    gl.shaderSource(fragmentShader, fragmentShaderSource);
    gl.compileShader(fragmentShader);
    
    gl.attachShader(shaderProgram, vertexShader);
    gl.attachShader(shaderProgram, fragmentShader);
    gl.linkProgram(shaderProgram);
    gl.useProgram(shaderProgram);
}

// 初始化缓冲区
function initBuffers() {
    // 顶点数据
    const vertices = new Float32Array([
        // x, y, z, u, v
        -1.0, -1.0, 0.0, 0.0, 0.0,
        1.0, -1.0, 0.0, 1.0, 0.0,
        1.0, 1.0, 0.0, 1.0, 1.0,
        -1.0, 1.0, 0.0, 0.0, 1.0
    ]);
    
    vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
    
    // 索引数据
    const indices = new Uint16Array([0, 1, 2, 0, 2, 3]);
    
    indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);
    
    // 设置属性指针
    const positionAttrib = gl.getAttribLocation(shaderProgram, 'aPosition');
    gl.vertexAttribPointer(positionAttrib, 3, gl.FLOAT, false, 20, 0);
    gl.enableVertexAttribArray(positionAttrib);
    
    const texCoordAttrib = gl.getAttribLocation(shaderProgram, 'aTexCoord');
    gl.vertexAttribPointer(texCoordAttrib, 2, gl.FLOAT, false, 20, 12);
    gl.enableVertexAttribArray(texCoordAttrib);
}

// 初始化纹理
function initTexture() {
    texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);
    
    // 临时纹理数据（实际项目中应加载图片）
    const level = 0;
    const internalFormat = gl.RGBA;
    const width = 2;
    const height = 2;
    const border = 0;
    const srcFormat = gl.RGBA;
    const srcType = gl.UNSIGNED_BYTE;
    const pixel = new Uint8Array([
        255, 0, 0, 255,   // 红色
        0, 255, 0, 255,   // 绿色
        0, 0, 255, 255,   // 蓝色
        255, 255, 0, 255  // 黄色
    ]);
    
    gl.texImage2D(gl.TEXTURE_2D, level, internalFormat, width, height, border, srcFormat, srcType, pixel);
    
    // 设置纹理参数
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
}

// 游戏主循环
function gameLoop() {
    if (!gameActive) return;
    
    render();
    requestAnimationFrame(gameLoop);
}

// 渲染场景
function render() {
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    
    // 渲染每个方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (gameBoard[y][x] !== -1) {
                renderTile(x, y, gameBoard[y][x]);
            }
        }
    }
}

// 渲染单个方块
function renderTile(x, y, type) {
    // 计算模型矩阵
    const modelMatrix = mat4.create();
    mat4.translate(modelMatrix, modelMatrix, [x * TILE_SIZE - 400, y * TILE_SIZE - 300, 0]);
    mat4.scale(modelMatrix, modelMatrix, [TILE_SIZE/2, TILE_SIZE/2, 1]);
    
    // 设置着色器变量
    const modelUniform = gl.getUniformLocation(shaderProgram, 'uModel');
    gl.uniformMatrix4fv(modelUniform, false, modelMatrix);
    
    // 设置纹理偏移（简化处理）
    const texOffset = gl.getUniformLocation(shaderProgram, 'uTexOffset');
    gl.uniform2f(texOffset, (type % 4) * 0.25, Math.floor(type / 4) * 0.25);
    
    // 绘制
    gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
}

// 开始计时器
function startTimer() {
    const timerElement = document.getElementById('timer');
    
    const timer = setInterval(() => {
        if (timeLeft > 0 && gameActive) {
            timeLeft--;
            timerElement.textContent = timeLeft;
        } else if (gameActive) {
            gameActive = false;
            clearInterval(timer);
            alert('时间到！最终得分: ' + score);
        }
    }, 1000);
}

// 路径检查函数（简化版）
function checkHorizontal(tile1, tile2) { /* 实现水平路径检查 */ }
function checkVertical(tile1, tile2) { /* 实现垂直路径检查 */ }
function checkOneCorner(tile1, tile2) { /* 实现一个拐角检查 */ }
function checkTwoCorners(tile1, tile2) { /* 实现两个拐角检查 */ }

// 启动游戏
window.onload = initGame;